package me.miguel19877.dev.gui;

import me.miguel19877.dev.managers.AchievementManager;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.utils.Achievement;
import me.miguel19877.dev.utils.AchievementPlayerData;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * GUI showing achievements for a specific category
 */
public class AchievementCategoryGUI {
    
    private static final int GUI_SIZE = 54; // 6 rows
    
    private final AchievementManager achievementManager = AchievementManager.getInstance();
    private final LanguageManager languageManager = LanguageManager.getInstance();
    
    /**
     * Open category GUI for a player
     */
    public void openCategoryGUI(Player player, String category) {
        String categoryDisplayName = languageManager.getMessage(player, "achievement.categories." + category);
        String categoryTitle = getCategoryColor(category) + "§l" + categoryDisplayName.toUpperCase();
        String guiTitle = languageManager.getMessage(player, "achievement.gui.category_title", categoryTitle);

        Inventory gui = Bukkit.createInventory(null, GUI_SIZE, guiTitle);
        
        // Get achievements for this category
        List<Achievement> categoryAchievements = getAchievementsForCategory(category);
        AchievementPlayerData playerData = achievementManager.getPlayerData(player.getUniqueId());

        if (categoryAchievements.isEmpty()) {
            // Add "no achievements" message
            ItemStack noAchievements = new ItemStack(Material.BARRIER);
            ItemMeta meta = noAchievements.getItemMeta();
            meta.setDisplayName(languageManager.getMessage(player, "achievement.gui.no_achievements_title"));
            List<String> lore = new ArrayList<>();
            lore.add(languageManager.getMessage(player, "achievement.gui.no_achievements_desc1"));
            lore.add(languageManager.getMessage(player, "achievement.gui.no_achievements_desc2"));
            meta.setLore(lore);
            noAchievements.setItemMeta(meta);
            gui.setItem(22, noAchievements); // Center slot
        } else {
            // Add achievements to GUI
            int slot = 10; // Start from slot 10 (second row, second item)
            for (Achievement achievement : categoryAchievements) {
                if (slot >= 44) break; // Don't go past row 5

                ItemStack achievementItem = createAchievementItem(player, achievement, playerData);
                gui.setItem(slot, achievementItem);

                slot++;
                // Skip to next row when reaching the end
                if (slot % 9 == 8) {
                    slot += 2; // Skip last slot of current row and first slot of next row
                }
            }
        }
        
        // Add back button
        gui.setItem(49, createBackButton(player));
        
        // Fill empty slots with glass panes
        fillEmptySlots(gui);
        
        player.openInventory(gui);
    }
    
    /**
     * Create an item representing an achievement
     */
    private ItemStack createAchievementItem(Player player, Achievement achievement, AchievementPlayerData playerData) {
        // Try to get the material from the achievement icon
        Material material = getMaterialFromIcon(achievement.getIcon());
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        boolean isCompleted = playerData != null && playerData.isCompleted(achievement.getId());
        long progress = playerData != null ? playerData.getProgress(achievement.getId()) : 0L;
        
        // Set display name
        String nameColor = isCompleted ? "§a" : "§7";
        String completedSymbol = isCompleted ? "§a✓ " : "§7○ ";
        meta.setDisplayName(completedSymbol + nameColor + achievement.getName());
        
        // Create lore
        List<String> lore = new ArrayList<>();
        
        // Add description
        if (achievement.getDescription() != null) {
            for (String descLine : achievement.getDescription()) {
                lore.add("§7" + descLine);
            }
            lore.add("");
        }
        
        // Add progress information
        if (isCompleted) {
            lore.add("§a§lCOMPLETADA!");
            long completionTime = playerData.getCompletionTime(achievement.getId());
            if (completionTime > 0) {
                lore.add(languageManager.getMessage(player, "achievement.progress.completed_on", formatDate(completionTime)));
            }
        } else {
            // Show progress
            double percentage = (double) progress / achievement.getThreshold() * 100;
            lore.add(languageManager.getMessage(player, "achievement.progress.current_progress",
                    String.valueOf(progress), String.valueOf(achievement.getThreshold())) +
                    " §7(" + String.format("%.1f", percentage) + "%)");

            // Progress bar
            lore.add(createProgressBar(progress, achievement.getThreshold()));
        }
        
        // Add rewards information
        lore.add("");
        lore.add(languageManager.getMessage(player, "achievement.progress.rewards_title"));
        if (achievement.getCommands() != null) {
            for (String command : achievement.getCommands()) {
                if (command.startsWith("money add")) {
                    String[] parts = command.split(" ");
                    if (parts.length >= 4) {
                        lore.add(languageManager.getMessage(player, "achievement.progress.reward_money", parts[3]));
                    }
                } else if (command.startsWith("broadcast")) {
                    lore.add(languageManager.getMessage(player, "achievement.progress.reward_broadcast"));
                } else if (command.startsWith("title")) {
                    lore.add(languageManager.getMessage(player, "achievement.progress.reward_title"));
                } else {
                    lore.add(languageManager.getMessage(player, "achievement.progress.reward_special"));
                }
            }
        }
        
        meta.setLore(lore);
        
        // Add enchantment glow for completed achievements
        if (isCompleted) {
            meta.addEnchant(Enchantment.DURABILITY, 1, true);
            meta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
        }
        
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * Create a progress bar string
     */
    private String createProgressBar(long progress, long threshold) {
        int barLength = 20;
        int filledBars = (int) ((double) progress / threshold * barLength);
        
        StringBuilder bar = new StringBuilder("§7[");
        for (int i = 0; i < barLength; i++) {
            if (i < filledBars) {
                bar.append("§a■");
            } else {
                bar.append("§7■");
            }
        }
        bar.append("§7]");
        
        return bar.toString();
    }
    
    /**
     * Create back button
     */
    private ItemStack createBackButton(Player player) {
        ItemStack backButton = new ItemStack(Material.ARROW);
        ItemMeta meta = backButton.getItemMeta();
        meta.setDisplayName(languageManager.getMessage(player, "achievement.gui.back_button"));
        List<String> lore = new ArrayList<>();
        lore.add(languageManager.getMessage(player, "achievement.gui.back_button_desc"));
        meta.setLore(lore);
        backButton.setItemMeta(meta);
        return backButton;
    }
    
    /**
     * Get material from achievement icon string
     */
    private Material getMaterialFromIcon(String icon) {
        if (icon == null) return Material.PAPER;
        
        try {
            return Material.valueOf(icon.toUpperCase());
        } catch (IllegalArgumentException e) {
            return Material.PAPER; // Fallback material
        }
    }
    
    /**
     * Get achievements for a specific category
     */
    private List<Achievement> getAchievementsForCategory(String category) {
        List<Achievement> categoryAchievements = new ArrayList<>();
        Map<String, Achievement> allAchievements = achievementManager.getAllAchievements();
        
        for (Achievement achievement : allAchievements.values()) {
            String achievementCategory = achievement.getCategory() != null ? achievement.getCategory().toLowerCase() : "bronze";
            if (achievementCategory.equals(category.toLowerCase())) {
                categoryAchievements.add(achievement);
            }
        }
        
        return categoryAchievements;
    }
    

    
    /**
     * Get color code for category
     */
    private String getCategoryColor(String category) {
        switch (category.toLowerCase()) {
            case "bronze": return "§6";
            case "silver": return "§7";
            case "gold": return "§e";
            case "platinum": return "§b";
            case "mythic": return "§5";
            default: return "§f";
        }
    }
    
    /**
     * Format timestamp to readable date
     */
    private String formatDate(long timestamp) {
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("dd/MM/yyyy HH:mm");
        return sdf.format(new java.util.Date(timestamp));
    }
    
    /**
     * Fill empty slots with glass panes
     */
    private void fillEmptySlots(Inventory gui) {
        ItemStack glassPane = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 7);
        ItemMeta meta = glassPane.getItemMeta();
        meta.setDisplayName(" ");
        glassPane.setItemMeta(meta);
        
        for (int i = 0; i < gui.getSize(); i++) {
            if (gui.getItem(i) == null) {
                gui.setItem(i, glassPane);
            }
        }
    }
    
    /**
     * Check if inventory is a category GUI
     */
    public static boolean isCategoryGUI(Inventory inventory) {
        return inventory.getSize() == GUI_SIZE && 
               inventory.getTitle() != null && 
               inventory.getTitle().startsWith("§6§lConquistas - ");
    }
    
    /**
     * Check if clicked item is the back button
     */
    public static boolean isBackButton(ItemStack item, int slot) {
        return slot == 49 && item != null && item.getType() == Material.ARROW;
    }
}
